{"name": "mAI Assistant", "productName": "mAI Assistant", "version": "1.0.0", "description": "mAI Assistant is a desktop application", "main": "main.js", "scripts": {"start": "electron .", "package-mac": "npx electron-packager . --overwrite --platform=darwin --arch=x64 --icon=assets/icons/mac/mai-logo.icns --prune=true --out=release-builds", "package-win": "npx electron-packager . --overwrite --asar=true --platform=win32 --arch=x64 --icon=assets/icons/win/mai-logo.ico --prune=true --out=release-builds --version-string.CompanyName=CE --version-string.FileDescription=CE --version-string.ProductName=\"ChatGPT\"", "package-linux": "npx electron-packager . --overwrite --platform=linux --arch=x64 --icon=assets/icons/png/mai-logo.png --prune=true --out=release-builds"}, "devDependencies": {"electron": "^22.0.0", "electron-packager": "^17.1.1"}, "dependencies": {"axios": "^1.6.2", "electron-store": "^8.1.0", "marked": "^15.0.8", "@azure/msal-browser": "^2.38.0"}}